package com.desaysv.workserver.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.core.task.AsyncTaskExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 流式传输配置类
 * 优化Android投屏等流式传输的性能，支持无限长视频流
 */
@Slf4j
@Configuration
public class StreamingConfig implements WebMvcConfigurer {

    /**
     * 配置异步支持
     */
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 移除超时限制，支持无限长视频流
        configurer.setDefaultTimeout(-1);
        
        // 设置异步执行器
        configurer.setTaskExecutor(streamingTaskExecutor());
        
        // 添加超时处理拦截器（用于监控和日志记录）
        configurer.registerCallableInterceptors(timeoutInterceptor());
        
        log.info("已配置流式传输异步支持: 无超时限制，支持无限长视频流, 线程池=streaming-executor");
    }

    /**
     * 流式传输专用线程池 - 优化支持无限长流
     */
    @Bean(name = "streamingTaskExecutor")
    public AsyncTaskExecutor streamingTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数 - 根据预期的并发流数量设置
        executor.setCorePoolSize(12);
        
        // 最大线程数 - 允许更多的并发流
        executor.setMaxPoolSize(48);
        
        // 队列容量 - 等待处理的任务队列
        executor.setQueueCapacity(200);
        
        // 线程名前缀
        executor.setThreadNamePrefix("streaming-");
        
        // 拒绝策略 - 使用调用者线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲存活时间 - 延长以避免频繁创建销毁
        executor.setKeepAliveSeconds(1800); // 30分钟
        
        // 不允许核心线程超时，确保长时间流的稳定性
        executor.setAllowCoreThreadTimeOut(false);
        
        // 等待任务完成后关闭 - 增加等待时间以支持长时间流
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(300); // 5分钟等待时间
        
        executor.initialize();
        
        log.info("已创建流式传输线程池: 核心线程=12, 最大线程=48, 队列容量=200, 支持无限长视频流");
        return executor;
    }

    /**
     * 超时处理拦截器 - 用于监控长时间运行的流
     */
    @Bean
    public TimeoutCallableProcessingInterceptor timeoutInterceptor() {
        return new TimeoutCallableProcessingInterceptor() {
            @Override
            public <T> Object handleTimeout(org.springframework.web.context.request.NativeWebRequest request, 
                                          java.util.concurrent.Callable<T> task) throws Exception {
                log.warn("流式传输任务异常超时 (这不应该发生在无限流配置下): URI={}", request.getDescription(false));
                return super.handleTimeout(request, task);
            }
            
            @Override
            public <T> void preProcess(org.springframework.web.context.request.NativeWebRequest request, 
                                     java.util.concurrent.Callable<T> task) throws Exception {
                log.debug("开始处理流式传输任务: URI={}", request.getDescription(false));
                super.preProcess(request, task);
            }
            
            @Override
            public <T> void postProcess(org.springframework.web.context.request.NativeWebRequest request, 
                                      java.util.concurrent.Callable<T> task, Object concurrentResult) throws Exception {
                log.debug("完成处理流式传输任务: URI={}", request.getDescription(false));
                super.postProcess(request, task, concurrentResult);
            }
        };
    }

    /**
     * 流式传输统计信息Bean
     */
    @Bean
    public StreamingStatisticsCollector streamingStatisticsCollector() {
        return new StreamingStatisticsCollector();
    }

    /**
     * 流式传输统计收集器 - 增强对长时间流的监控
     */
    public static class StreamingStatisticsCollector {
        private volatile int totalActiveStreams = 0;
        private volatile long totalBytesTransferred = 0;
        private volatile long totalStreamsCreated = 0;
        private volatile long totalLongRunningStreams = 0; // 运行超过1小时的流
        private final long startTime = System.currentTimeMillis();

        public void incrementActiveStreams() {
            totalActiveStreams++;
            totalStreamsCreated++;
        }

        public void decrementActiveStreams() {
            totalActiveStreams = Math.max(0, totalActiveStreams - 1);
        }
        
        public void incrementLongRunningStreams() {
            totalLongRunningStreams++;
        }

        public void addBytesTransferred(long bytes) {
            totalBytesTransferred += bytes;
        }

        public int getTotalActiveStreams() {
            return totalActiveStreams;
        }

        public long getTotalBytesTransferred() {
            return totalBytesTransferred;
        }

        public long getTotalStreamsCreated() {
            return totalStreamsCreated;
        }
        
        public long getTotalLongRunningStreams() {
            return totalLongRunningStreams;
        }

        public double getAverageStreamDuration() {
            long uptime = System.currentTimeMillis() - startTime;
            return totalStreamsCreated > 0 ? (double) uptime / totalStreamsCreated : 0;
        }

        public String getStatisticsSummary() {
            long uptimeHours = (System.currentTimeMillis() - startTime) / (1000 * 60 * 60);
            double transferredGB = totalBytesTransferred / (1024.0 * 1024.0 * 1024.0);
            
            return String.format(
                "流式传输统计 [运行时间: %d小时] - 活跃流: %d, 总创建流: %d, 长时间流: %d, 总传输: %.2fGB, 平均流时长: %.2fs",
                uptimeHours, totalActiveStreams, totalStreamsCreated, totalLongRunningStreams, transferredGB, 
                getAverageStreamDuration() / 1000.0
            );
        }
        
        /**
         * 获取内存使用情况
         */
        public String getMemoryUsage() {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            return String.format(
                "内存使用: %.2fMB/%.2fMB (%.1f%%), 活跃流: %d",
                usedMemory / (1024.0 * 1024.0),
                maxMemory / (1024.0 * 1024.0),
                (usedMemory * 100.0) / maxMemory,
                totalActiveStreams
            );
        }
    }
} 