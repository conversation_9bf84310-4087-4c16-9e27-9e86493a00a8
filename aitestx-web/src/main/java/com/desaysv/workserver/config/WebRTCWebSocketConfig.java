package com.desaysv.workserver.config;

import com.desaysv.workserver.controller.screen.AndroidWebRTCController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebRTC WebSocket配置
 * 配置WebSocket端点以支持WebRTC信令
 */
@Configuration
@EnableWebSocket
public class WebRTCWebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private AndroidWebRTCController.WebRTCWebSocketHandler webRTCWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webRTCWebSocketHandler, "/android/webrtc/signaling")
                .setAllowedOrigins("*") // 在生产环境中应该限制允许的源
                .withSockJS(); // 启用SockJS支持，提供更好的兼容性
    }
} 