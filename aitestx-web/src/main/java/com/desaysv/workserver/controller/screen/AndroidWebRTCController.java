package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Android WebRTC投屏控制器
 * 使用WebRTC技术提供低延迟、高质量的实时投屏服务
 */
@Slf4j
@RestController
@RequestMapping("/android/webrtc")
@Lazy
public class AndroidWebRTCController {

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    @Autowired
    private ObjectMapper objectMapper;

    // 用于跟踪活跃的WebRTC连接
    private final ConcurrentHashMap<String, WebRTCSession> activeSessions = new ConcurrentHashMap<>();
    
    // 性能监控 - 跟踪每个设备的WebRTC统计
    private final ConcurrentHashMap<String, WebRTCStatistics> sessionStats = new ConcurrentHashMap<>();

    /**
     * WebRTC会话管理类
     */
    private static class WebRTCSession {
        private final String deviceName;
        private final WebSocketSession webSocketSession;
        private final AtomicBoolean isActive;
        private final AtomicLong sessionStartTime;
        private final AtomicLong totalBytesTransferred;
        private final AtomicLong totalFramesTransferred;
        private volatile int width;
        private volatile int height;
        private volatile int bitRate;
        private volatile String sessionId;

        public WebRTCSession(String deviceName, WebSocketSession webSocketSession) {
            this.deviceName = deviceName;
            this.webSocketSession = webSocketSession;
            this.isActive = new AtomicBoolean(true);
            this.sessionStartTime = new AtomicLong(System.currentTimeMillis());
            this.totalBytesTransferred = new AtomicLong(0);
            this.totalFramesTransferred = new AtomicLong(0);
        }

        // Getters and Setters
        public String getDeviceName() { return deviceName; }
        public WebSocketSession getWebSocketSession() { return webSocketSession; }
        public boolean isActive() { return isActive.get(); }
        public void setActive(boolean active) { isActive.set(active); }
        public long getSessionStartTime() { return sessionStartTime.get(); }
        public long getTotalBytesTransferred() { return totalBytesTransferred.get(); }
        public long getTotalFramesTransferred() { return totalFramesTransferred.get(); }
        public void addBytesTransferred(long bytes) { totalBytesTransferred.addAndGet(bytes); }
        public void addFramesTransferred(long frames) { totalFramesTransferred.addAndGet(frames); }
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
        public int getBitRate() { return bitRate; }
        public void setBitRate(int bitRate) { this.bitRate = bitRate; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    }

    /**
     * WebRTC统计信息类
     */
    private static class WebRTCStatistics {
        private final AtomicLong totalSessions = new AtomicLong(0);
        private final AtomicLong totalBytesTransferred = new AtomicLong(0);
        private final AtomicLong totalFramesTransferred = new AtomicLong(0);
        private volatile long lastSessionTime = System.currentTimeMillis();
        private volatile double avgSessionDuration = 0.0;
        private volatile double avgThroughput = 0.0; // bytes/sec
        private volatile double avgFrameRate = 0.0; // fps

        public void updateStats(long bytes, long frames, long sessionDuration) {
            totalBytesTransferred.addAndGet(bytes);
            totalFramesTransferred.addAndGet(frames);
            
            if (sessionDuration > 0) {
                avgSessionDuration = (avgSessionDuration + sessionDuration) / 2.0;
                avgThroughput = (double) totalBytesTransferred.get() / (totalSessions.get() * avgSessionDuration / 1000.0);
                avgFrameRate = (double) totalFramesTransferred.get() / (totalSessions.get() * avgSessionDuration / 1000.0);
            }
        }

        public void incrementSessionCount() {
            totalSessions.incrementAndGet();
            lastSessionTime = System.currentTimeMillis();
        }

        // Getters
        public long getTotalSessions() { return totalSessions.get(); }
        public long getTotalBytesTransferred() { return totalBytesTransferred.get(); }
        public long getTotalFramesTransferred() { return totalFramesTransferred.get(); }
        public double getAvgSessionDuration() { return avgSessionDuration; }
        public double getAvgThroughput() { return avgThroughput; }
        public double getAvgFrameRate() { return avgFrameRate; }
    }

    /**
     * WebRTC信令消息类
     */
    public static class WebRTCMessage {
        private String type; // offer, answer, ice-candidate, etc.
        private String sessionId;
        private String deviceName;
        private Object data;
        private int width;
        private int height;
        private int bitRate;

        // Constructors
        public WebRTCMessage() {}

        public WebRTCMessage(String type, String sessionId, String deviceName) {
            this.type = type;
            this.sessionId = sessionId;
            this.deviceName = deviceName;
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        public String getDeviceName() { return deviceName; }
        public void setDeviceName(String deviceName) { this.deviceName = deviceName; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }
        public int getBitRate() { return bitRate; }
        public void setBitRate(int bitRate) { this.bitRate = bitRate; }
    }

    /**
     * 启动WebRTC投屏会话
     */
    @PostMapping("/session/{deviceName}")
    public ResponseEntity<WebRTCMessage> startWebRTCSession(
            @PathVariable String deviceName,
            @RequestParam(defaultValue = "0") int width,
            @RequestParam(defaultValue = "0") int height,
            @RequestParam(defaultValue = "0") int bitRate) {

        log.info("请求启动WebRTC投屏会话: 设备={}, 分辨率={}x{}, 比特率={}", 
                deviceName, width, height, bitRate);

        try {
            // 检查设备是否可用
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice == null) {
                log.error("未找到Android设备: {}", deviceName);
                return ResponseEntity.notFound().build();
            }

            // 生成会话ID
            String sessionId = generateSessionId(deviceName);
            
            // 创建响应消息
            WebRTCMessage response = new WebRTCMessage();
            response.setType("session-created");
            response.setSessionId(sessionId);
            response.setDeviceName(deviceName);
            response.setWidth(width);
            response.setHeight(height);
            response.setBitRate(bitRate);

            // 更新统计信息
            WebRTCStatistics stats = sessionStats.computeIfAbsent(deviceName, k -> new WebRTCStatistics());
            stats.incrementSessionCount();

            log.info("WebRTC会话已创建: 设备={}, 会话ID={}", deviceName, sessionId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("启动WebRTC会话失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 停止WebRTC投屏会话
     */
    @DeleteMapping("/session/{deviceName}/{sessionId}")
    public ResponseEntity<String> stopWebRTCSession(
            @PathVariable String deviceName,
            @PathVariable String sessionId) {

        log.info("请求停止WebRTC投屏会话: 设备={}, 会话ID={}", deviceName, sessionId);

        try {
            WebRTCSession session = activeSessions.get(sessionId);
            if (session != null && session.getDeviceName().equals(deviceName)) {
                session.setActive(false);
                activeSessions.remove(sessionId);
                
                // 更新统计信息
                long sessionDuration = System.currentTimeMillis() - session.getSessionStartTime();
                WebRTCStatistics stats = sessionStats.get(deviceName);
                if (stats != null) {
                    stats.updateStats(session.getTotalBytesTransferred(), 
                                   session.getTotalFramesTransferred(), 
                                   sessionDuration);
                }

                log.info("WebRTC会话已停止: 设备={}, 会话ID={}, 持续时间={}ms", 
                        deviceName, sessionId, sessionDuration);
                return ResponseEntity.ok("会话已停止");
            } else {
                log.warn("未找到指定的WebRTC会话: 设备={}, 会话ID={}", deviceName, sessionId);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("停止WebRTC会话失败: 设备={}, 会话ID={}, 错误={}", 
                    deviceName, sessionId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取WebRTC会话统计信息
     */
    @GetMapping("/stats/{deviceName}")
    public ResponseEntity<WebRTCStatistics> getWebRTCStats(@PathVariable String deviceName) {
        WebRTCStatistics stats = sessionStats.get(deviceName);
        if (stats != null) {
            return ResponseEntity.ok(stats);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取所有活跃的WebRTC会话
     */
    @GetMapping("/sessions")
    public ResponseEntity<String> getActiveSessions() {
        StringBuilder overview = new StringBuilder();
        overview.append("当前活跃的WebRTC会话数量: ").append(activeSessions.size()).append("\n");
        
        for (WebRTCSession session : activeSessions.values()) {
            overview.append("设备: ").append(session.getDeviceName())
                    .append(", 会话ID: ").append(session.getSessionId())
                    .append(", 分辨率: ").append(session.getWidth()).append("x").append(session.getHeight())
                    .append(", 比特率: ").append(session.getBitRate())
                    .append(", 传输字节: ").append(session.getTotalBytesTransferred())
                    .append(", 传输帧数: ").append(session.getTotalFramesTransferred())
                    .append("\n");
        }
        
        return ResponseEntity.ok(overview.toString());
    }

    /**
     * WebSocket处理器 - 处理WebRTC信令
     */
    @Component
    public class WebRTCWebSocketHandler extends TextWebSocketHandler {

        @Override
        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
            log.info("WebRTC WebSocket连接已建立: {}", session.getId());
        }

        @Override
        protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
            try {
                WebRTCMessage webrtcMessage = objectMapper.readValue(message.getPayload(), WebRTCMessage.class);
                handleWebRTCMessage(session, webrtcMessage);
            } catch (Exception e) {
                log.error("处理WebRTC消息失败: {}", e.getMessage(), e);
                sendErrorMessage(session, "消息处理失败: " + e.getMessage());
            }
        }

        @Override
        public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
            log.info("WebRTC WebSocket连接已关闭: {}, 状态: {}", session.getId(), status);
            
            // 清理相关会话
            String sessionId = session.getId();
            WebRTCSession webrtcSession = activeSessions.remove(sessionId);
            if (webrtcSession != null) {
                webrtcSession.setActive(false);
                log.info("清理WebRTC会话: 设备={}, 会话ID={}", 
                        webrtcSession.getDeviceName(), webrtcSession.getSessionId());
            }
        }

        @Override
        public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
            log.error("WebRTC WebSocket传输错误: {}", exception.getMessage(), exception);
        }
    }

    /**
     * 处理WebRTC消息
     */
    private void handleWebRTCMessage(WebSocketSession session, WebRTCMessage message) throws Exception {
        String messageType = message.getType();
        String deviceName = message.getDeviceName();
        String sessionId = message.getSessionId();

        log.debug("收到WebRTC消息: 类型={}, 设备={}, 会话ID={}", messageType, deviceName, sessionId);

        switch (messageType) {
            case "offer":
                handleOffer(session, message);
                break;
            case "answer":
                handleAnswer(session, message);
                break;
            case "ice-candidate":
                handleIceCandidate(session, message);
                break;
            case "start-stream":
                handleStartStream(session, message);
                break;
            case "stop-stream":
                handleStopStream(session, message);
                break;
            default:
                log.warn("未知的WebRTC消息类型: {}", messageType);
                sendErrorMessage(session, "未知的消息类型: " + messageType);
        }
    }

    /**
     * 处理WebRTC Offer
     */
    private void handleOffer(WebSocketSession session, WebRTCMessage message) throws Exception {
        String deviceName = message.getDeviceName();
        String sessionId = message.getSessionId();

        // 验证设备
        AndroidDevice androidDevice = getAndroidDevice(deviceName);
        if (androidDevice == null) {
            sendErrorMessage(session, "设备不可用: " + deviceName);
            return;
        }

        // 创建WebRTC会话
        WebRTCSession webrtcSession = new WebRTCSession(deviceName, session);
        webrtcSession.setSessionId(sessionId);
        webrtcSession.setWidth(message.getWidth());
        webrtcSession.setHeight(message.getHeight());
        webrtcSession.setBitRate(message.getBitRate());
        
        activeSessions.put(sessionId, webrtcSession);

        // 发送确认消息
        WebRTCMessage response = new WebRTCMessage("offer-ack", sessionId, deviceName);
        sendMessage(session, response);

        log.info("WebRTC Offer已处理: 设备={}, 会话ID={}", deviceName, sessionId);
    }

    /**
     * 处理WebRTC Answer
     */
    private void handleAnswer(WebSocketSession session, WebRTCMessage message) throws Exception {
        String sessionId = message.getSessionId();
        WebRTCSession webrtcSession = activeSessions.get(sessionId);
        
        if (webrtcSession != null) {
            // 发送确认消息
            WebRTCMessage response = new WebRTCMessage("answer-ack", sessionId, webrtcSession.getDeviceName());
            sendMessage(session, response);
            
            log.info("WebRTC Answer已处理: 会话ID={}", sessionId);
        } else {
            sendErrorMessage(session, "会话不存在: " + sessionId);
        }
    }

    /**
     * 处理ICE候选
     */
    private void handleIceCandidate(WebSocketSession session, WebRTCMessage message) throws Exception {
        String sessionId = message.getSessionId();
        WebRTCSession webrtcSession = activeSessions.get(sessionId);
        
        if (webrtcSession != null) {
            // 转发ICE候选到其他参与者（如果有的话）
            WebRTCMessage response = new WebRTCMessage("ice-candidate", sessionId, webrtcSession.getDeviceName());
            response.setData(message.getData());
            sendMessage(session, response);
            
            log.debug("ICE候选已处理: 会话ID={}", sessionId);
        } else {
            sendErrorMessage(session, "会话不存在: " + sessionId);
        }
    }

    /**
     * 处理开始流传输
     */
    private void handleStartStream(WebSocketSession session, WebRTCMessage message) throws Exception {
        String sessionId = message.getSessionId();
        WebRTCSession webrtcSession = activeSessions.get(sessionId);
        
        if (webrtcSession != null) {
            String deviceName = webrtcSession.getDeviceName();
            
            try {
                // 启动Android设备的视频流
                AndroidDevice androidDevice = getAndroidDevice(deviceName);
                if (androidDevice != null) {
                    InputStream videoStream = androidDevice.startVideoStream(
                            webrtcSession.getWidth(), 
                            webrtcSession.getHeight(), 
                            webrtcSession.getBitRate());
                    
                    if (videoStream != null) {
                        // 开始视频流处理
                        startVideoStreamProcessing(webrtcSession, videoStream, androidDevice);
                        
                        WebRTCMessage response = new WebRTCMessage("stream-started", sessionId, deviceName);
                        sendMessage(session, response);
                        
                        log.info("视频流已启动: 设备={}, 会话ID={}", deviceName, sessionId);
                    } else {
                        sendErrorMessage(session, "无法启动视频流");
                    }
                } else {
                    sendErrorMessage(session, "设备不可用: " + deviceName);
                }
            } catch (Exception e) {
                log.error("启动视频流失败: 设备={}, 会话ID={}, 错误={}", 
                        deviceName, sessionId, e.getMessage(), e);
                sendErrorMessage(session, "启动视频流失败: " + e.getMessage());
            }
        } else {
            sendErrorMessage(session, "会话不存在: " + sessionId);
        }
    }

    /**
     * 处理停止流传输
     */
    private void handleStopStream(WebSocketSession session, WebRTCMessage message) throws Exception {
        String sessionId = message.getSessionId();
        WebRTCSession webrtcSession = activeSessions.get(sessionId);
        
        if (webrtcSession != null) {
            String deviceName = webrtcSession.getDeviceName();
            
            // 停止Android设备的视频流
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice != null) {
                androidDevice.stopVideoStream();
            }
            
            webrtcSession.setActive(false);
            
            WebRTCMessage response = new WebRTCMessage("stream-stopped", sessionId, deviceName);
            sendMessage(session, response);
            
            log.info("视频流已停止: 设备={}, 会话ID={}", deviceName, sessionId);
        } else {
            sendErrorMessage(session, "会话不存在: " + sessionId);
        }
    }

    /**
     * 开始视频流处理
     */
    private void startVideoStreamProcessing(WebRTCSession webrtcSession, InputStream videoStream, AndroidDevice androidDevice) {
        Thread streamThread = new Thread(() -> {
            try {
                byte[] buffer = new byte[8192];
                int bytesRead;
                
                log.info("开始处理视频流: 设备={}, 会话ID={}", 
                        webrtcSession.getDeviceName(), webrtcSession.getSessionId());
                
                while ((bytesRead = videoStream.read(buffer)) != -1 && webrtcSession.isActive()) {
                    // 将视频数据发送到WebRTC客户端
                    sendVideoData(webrtcSession, buffer, bytesRead);
                    
                    // 更新统计信息
                    webrtcSession.addBytesTransferred(bytesRead);
                    webrtcSession.addFramesTransferred(1); // 简化处理，每个数据块算一帧
                }
                
                log.info("视频流处理结束: 设备={}, 会话ID={}", 
                        webrtcSession.getDeviceName(), webrtcSession.getSessionId());
                
            } catch (IOException e) {
                log.error("视频流处理异常: 设备={}, 会话ID={}, 错误={}", 
                        webrtcSession.getDeviceName(), webrtcSession.getSessionId(), e.getMessage(), e);
            } finally {
                try {
                    videoStream.close();
                } catch (IOException e) {
                    log.warn("关闭视频流失败: {}", e.getMessage());
                }
            }
        });
        
        streamThread.setDaemon(true);
        streamThread.start();
    }

    /**
     * 发送视频数据到WebRTC客户端
     */
    private void sendVideoData(WebRTCSession webrtcSession, byte[] data, int length) {
        try {
            WebRTCMessage message = new WebRTCMessage("video-data", 
                    webrtcSession.getSessionId(), webrtcSession.getDeviceName());
            message.setData(data);
            
            sendMessage(webrtcSession.getWebSocketSession(), message);
        } catch (Exception e) {
            log.error("发送视频数据失败: 会话ID={}, 错误={}", 
                    webrtcSession.getSessionId(), e.getMessage(), e);
            webrtcSession.setActive(false);
        }
    }

    /**
     * 发送消息到WebSocket客户端
     */
    private void sendMessage(WebSocketSession session, WebRTCMessage message) throws Exception {
        String jsonMessage = objectMapper.writeValueAsString(message);
        session.sendMessage(new TextMessage(jsonMessage));
    }

    /**
     * 发送错误消息到WebSocket客户端
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) throws Exception {
        WebRTCMessage errorMsg = new WebRTCMessage("error", null, null);
        errorMsg.setData(errorMessage);
        sendMessage(session, errorMsg);
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId(String deviceName) {
        return deviceName + "_" + System.currentTimeMillis() + "_" + 
               String.valueOf((int)(Math.random() * 10000));
    }

    /**
     * 获取Android设备实例
     */
    private AndroidDevice getAndroidDevice(String deviceName) {
        try {
            Object device = deviceRegisterManager.getDevice(deviceName);
            if (device instanceof AndroidDevice) {
                return (AndroidDevice) device;
            } else {
                log.warn("设备 {} 不是Android设备类型: {}", deviceName,
                        device != null ? device.getClass().getSimpleName() : "null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取Android设备失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取当前活跃的WebRTC会话数量
     */
    public int getActiveSessionCount() {
        return activeSessions.size();
    }

    /**
     * 停止所有活跃的WebRTC会话
     */
    public void stopAllSessions() {
        log.info("停止所有活跃的WebRTC会话，当前数量: {}", activeSessions.size());
        
        for (WebRTCSession session : activeSessions.values()) {
            session.setActive(false);
            
            // 停止Android设备的视频流
            try {
                AndroidDevice androidDevice = getAndroidDevice(session.getDeviceName());
                if (androidDevice != null) {
                    androidDevice.stopVideoStream();
                }
            } catch (Exception e) {
                log.warn("停止设备视频流失败: 设备={}, 错误={}", 
                        session.getDeviceName(), e.getMessage());
            }
        }
        
        activeSessions.clear();
        log.info("所有WebRTC会话已停止");
    }
} 