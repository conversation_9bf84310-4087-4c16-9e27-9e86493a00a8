package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.WebAsyncTask;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Android投屏控制器
 * 优化版本 - 支持高性能HTTP流式传输，特别针对2K/4K车机屏幕优化
 */
@Slf4j
@RestController
@RequestMapping("/android/screencap")
@Lazy
public class AndroidScreencapController {

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    // 用于跟踪活跃的视频流，以便在设备关闭时自动停止
    private final ConcurrentHashMap<String, AtomicBoolean> activeStreams = new ConcurrentHashMap<>();

    // 性能监控 - 跟踪每个设备的流传输统计
    private final ConcurrentHashMap<String, StreamStatistics> streamStats = new ConcurrentHashMap<>();

    // 针对4K车机屏幕优化的缓冲区配置
    private static final int INITIAL_BUFFER_SIZE_4K = 512 * 1024;       // 512KB初始缓冲区（4K屏幕）
    private static final int MAX_BUFFER_SIZE_4K = 8 * 1024 * 1024;      // 8MB最大缓冲区（4K屏幕）
    private static final int MIN_BUFFER_SIZE_4K = 256 * 1024;           // 256KB最小缓冲区（4K屏幕）
    
    // 针对2K车机屏幕优化的缓冲区配置
    private static final int INITIAL_BUFFER_SIZE_2K = 128 * 1024;       // 128KB初始缓冲区（2K屏幕）
    private static final int MAX_BUFFER_SIZE_2K = 2 * 1024 * 1024;      // 2MB最大缓冲区（2K屏幕）
    private static final int MIN_BUFFER_SIZE_2K = 64 * 1024;            // 64KB最小缓冲区（2K屏幕）
    
    // 普通屏幕的缓冲区配置
    private static final int INITIAL_BUFFER_SIZE = 32 * 1024;           // 32KB初始缓冲区
    private static final int MAX_BUFFER_SIZE = 512 * 1024;              // 512KB最大缓冲区
    private static final int MIN_BUFFER_SIZE = 8 * 1024;                // 8KB最小缓冲区
    
    private static final long BUFFER_ADJUSTMENT_INTERVAL = 1500;         // 缓冲区调整间隔(毫秒) - 更频繁调整
    private static final long PERFORMANCE_LOG_INTERVAL = 8000;          // 性能日志间隔(毫秒) - 更频繁记录
    private static final int BACKPRESSURE_THRESHOLD_4K = 8 * 1024 * 1024;  // 4K屏幕背压阈值 8MB
    private static final int BACKPRESSURE_THRESHOLD_2K = 4 * 1024 * 1024;  // 2K屏幕背压阈值 4MB
    private static final int BACKPRESSURE_THRESHOLD = 1024 * 1024;         // 普通屏幕背压阈值 1MB
    
    // 分辨率检测阈值
    private static final int ULTRA_HIGH_RESOLUTION_THRESHOLD_WIDTH = 3840;  // 宽度>=3840认为是4K分辨率
    private static final int ULTRA_HIGH_RESOLUTION_THRESHOLD_HEIGHT = 2160; // 高度>=2160认为是4K分辨率
    private static final int HIGH_RESOLUTION_THRESHOLD_WIDTH = 1920;        // 宽度>=1920认为是2K分辨率
    private static final int HIGH_RESOLUTION_THRESHOLD_HEIGHT = 1080;       // 高度>=1080认为是2K分辨率

    /**
     * 流传输统计信息 - 增强2K/4K屏幕支持
     */
    private static class StreamStatistics {
        private final AtomicLong totalBytes = new AtomicLong(0);
        private final AtomicLong totalFrames = new AtomicLong(0);
        private volatile long startTime = System.currentTimeMillis();
        private volatile long lastFrameTime = System.currentTimeMillis();
        private volatile int currentBufferSize;
        private volatile double avgThroughput = 0.0; // bytes/sec
        private volatile long lastThroughputUpdate = System.currentTimeMillis();
        private volatile ResolutionType resolutionType = ResolutionType.STANDARD;
        private volatile int screenWidth = 0;
        private volatile int screenHeight = 0;
        private final AtomicLong totalCompressionTime = new AtomicLong(0); // 压缩耗时统计
        private volatile double compressionRatio = 1.0; // 压缩比

        public StreamStatistics(int width, int height) {
            this.screenWidth = width;
            this.screenHeight = height;
            this.resolutionType = determineResolutionType(width, height);
            this.currentBufferSize = getInitialBufferSizeForResolution(resolutionType);
            
            log.info("初始化流统计: 分辨率={}x{}, 类型={}, 初始缓冲区={}KB", 
                    width, height, resolutionType, currentBufferSize / 1024);
        }

        private ResolutionType determineResolutionType(int width, int height) {
            if (width >= ULTRA_HIGH_RESOLUTION_THRESHOLD_WIDTH || height >= ULTRA_HIGH_RESOLUTION_THRESHOLD_HEIGHT) {
                return ResolutionType.ULTRA_HIGH_4K;
            } else if (width >= HIGH_RESOLUTION_THRESHOLD_WIDTH || height >= HIGH_RESOLUTION_THRESHOLD_HEIGHT) {
                return ResolutionType.HIGH_2K;
            } else {
                return ResolutionType.STANDARD;
            }
        }

        private int getInitialBufferSizeForResolution(ResolutionType type) {
            switch (type) {
                case ULTRA_HIGH_4K: return INITIAL_BUFFER_SIZE_4K;
                case HIGH_2K: return INITIAL_BUFFER_SIZE_2K;
                default: return INITIAL_BUFFER_SIZE;
            }
        }

        public void updateStats(int bytesRead) {
            totalBytes.addAndGet(bytesRead);
            totalFrames.incrementAndGet();
            lastFrameTime = System.currentTimeMillis();

            // 更新吞吐量统计
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastThroughputUpdate >= 1000) { // 每秒更新一次
                long elapsedTime = currentTime - startTime;
                if (elapsedTime > 0) {
                    avgThroughput = (double) totalBytes.get() / (elapsedTime / 1000.0);
                }
                lastThroughputUpdate = currentTime;
            }
        }

        public void updateCompressionStats(long compressionTime, double ratio) {
            totalCompressionTime.addAndGet(compressionTime);
            this.compressionRatio = ratio;
        }

        // Getters
        public long getTotalBytes() { return totalBytes.get(); }
        public long getTotalFrames() { return totalFrames.get(); }
        public double getAvgThroughput() { return avgThroughput; }
        public int getCurrentBufferSize() { return currentBufferSize; }
        public ResolutionType getResolutionType() { return resolutionType; }
        public boolean isHighResolution() { return resolutionType != ResolutionType.STANDARD; }
        public boolean is4KResolution() { return resolutionType == ResolutionType.ULTRA_HIGH_4K; }
        public boolean is2KResolution() { return resolutionType == ResolutionType.HIGH_2K; }
        public int getScreenWidth() { return screenWidth; }
        public int getScreenHeight() { return screenHeight; }
        public long getTotalCompressionTime() { return totalCompressionTime.get(); }
        public double getCompressionRatio() { return compressionRatio; }

        public void setCurrentBufferSize(int size) {
            int maxSize = getMaxBufferSize();
            int minSize = getMinBufferSize();
            this.currentBufferSize = Math.max(minSize, Math.min(size, maxSize));
        }

        public int getMaxBufferSize() {
            switch (resolutionType) {
                case ULTRA_HIGH_4K: return MAX_BUFFER_SIZE_4K;
                case HIGH_2K: return MAX_BUFFER_SIZE_2K;
                default: return MAX_BUFFER_SIZE;
            }
        }

        public int getMinBufferSize() {
            switch (resolutionType) {
                case ULTRA_HIGH_4K: return MIN_BUFFER_SIZE_4K;
                case HIGH_2K: return MIN_BUFFER_SIZE_2K;
                default: return MIN_BUFFER_SIZE;
            }
        }

        public int getBackpressureThreshold() {
            switch (resolutionType) {
                case ULTRA_HIGH_4K: return BACKPRESSURE_THRESHOLD_4K;
                case HIGH_2K: return BACKPRESSURE_THRESHOLD_2K;
                default: return BACKPRESSURE_THRESHOLD;
            }
        }
    }

    /**
     * 分辨率类型枚举
     */
    public enum ResolutionType {
        STANDARD("标准"),
        HIGH_2K("2K高分辨率"),
        ULTRA_HIGH_4K("4K超高分辨率");

        private final String description;

        ResolutionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        @Override
        public String toString() {
            return description;
        }
    }

    /**
     * 启动Android设备视频流（默认参数）
     */
    @GetMapping("/stream/{deviceName}")
    public WebAsyncTask<ResponseEntity<StreamingResponseBody>> startVideoStream(@PathVariable String deviceName) {
        return startVideoStreamWithParams(deviceName, 0, 0, 0);
    }

    /**
     * 启动Android设备视频流（带参数）- 2K/4K屏幕优化版本
     */
    @GetMapping(value = "/stream/{deviceName}/params", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public WebAsyncTask<ResponseEntity<StreamingResponseBody>> startVideoStreamWithParams(
            @PathVariable String deviceName,
            @RequestParam(defaultValue = "0") int width,
            @RequestParam(defaultValue = "0") int height,
            @RequestParam(defaultValue = "0") int bitRate) {

        // 检测分辨率类型
        ResolutionType resType = determineResolutionType(width, height);
        
        log.info("请求Android设备视频流: 设备={}, 分辨率={}x{} ({}), 比特率={}",
                deviceName, width, height, resType.getDescription(), bitRate);

        // 根据分辨率类型设置超时时间
        long timeout = getTimeoutForResolution(resType);
        
        WebAsyncTask<ResponseEntity<StreamingResponseBody>> asyncTask = new WebAsyncTask<>(timeout, () -> {
            StreamStatistics stats = null;
            try {
                // 获取Android设备实例
                AndroidDevice androidDevice = getAndroidDevice(deviceName);
                if (androidDevice == null) {
                    log.error("未找到Android设备: {}", deviceName);
                    return ResponseEntity.notFound().build();
                }

                // 启动视频流
                InputStream videoStream = androidDevice.startVideoStream(width, height, bitRate);
                if (videoStream == null) {
                    log.error("无法启动Android设备视频流: {}", deviceName);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
                }

                // 初始化流统计和状态
                stats = new StreamStatistics(width, height);
                streamStats.put(deviceName, stats);
                AtomicBoolean streamActive = new AtomicBoolean(true);
                activeStreams.put(deviceName, streamActive);

                // 创建优化的流式响应体
                StreamingResponseBody streamingResponseBody = createOptimizedStreamingBody(
                        videoStream, deviceName, streamActive, androidDevice, stats);

                // 设置优化的响应头
                HttpHeaders headers = createOptimizedHeaders(deviceName, width, height, bitRate);

                return ResponseEntity.ok()
                        .headers(headers)
                        .body(streamingResponseBody);

            } catch (IOException e) {
                log.error("启动Android设备视频流失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
                cleanupStreamResources(deviceName, null, stats);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            } catch (Exception e) {
                log.error("处理Android设备视频流请求时发生未知错误: 设备={}, 错误={}", deviceName, e.getMessage(), e);
                cleanupStreamResources(deviceName, null, stats);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        });

        // 设置异步任务的回调
        asyncTask.onCompletion(() -> log.info("视频流异步任务完成: {} ({})", deviceName, resType.getDescription()));

        return asyncTask;
    }

    /**
     * 确定分辨率类型
     */
    private ResolutionType determineResolutionType(int width, int height) {
        if (width >= ULTRA_HIGH_RESOLUTION_THRESHOLD_WIDTH || height >= ULTRA_HIGH_RESOLUTION_THRESHOLD_HEIGHT) {
            return ResolutionType.ULTRA_HIGH_4K;
        } else if (width >= HIGH_RESOLUTION_THRESHOLD_WIDTH || height >= HIGH_RESOLUTION_THRESHOLD_HEIGHT) {
            return ResolutionType.HIGH_2K;
        } else {
            return ResolutionType.STANDARD;
        }
    }

    /**
     * 根据分辨率类型获取超时时间
     */
    private long getTimeoutForResolution(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return 900000L; // 4K屏幕15分钟
            case HIGH_2K: return 600000L;       // 2K屏幕10分钟
            default: return 300000L;            // 普通屏幕5分钟
        }
    }

    /**
     * 创建优化的流式响应体 - 2K屏幕优化
     */
    private StreamingResponseBody createOptimizedStreamingBody(
            InputStream videoStream, String deviceName, AtomicBoolean streamActive,
            AndroidDevice androidDevice, StreamStatistics stats) {

        return outputStream -> {
            try (InputStream inputStream = videoStream) {
                byte[] buffer = new byte[stats.getCurrentBufferSize()];
                int bytesRead;
                long lastBufferAdjustTime = System.currentTimeMillis();
                long lastPerformanceLogTime = System.currentTimeMillis();
                long consecutiveSlowReads = 0;
                long lastMemoryCheckTime = System.currentTimeMillis();

                String resType = stats.isHighResolution() ? "2K高分辨率" : "标准分辨率";
                log.info("开始传输Android设备视频流: {} ({}, 初始缓冲区: {}KB)",
                        deviceName, resType, stats.getCurrentBufferSize() / 1024);

                while ((bytesRead = inputStream.read(buffer)) != -1 && streamActive.get()) {
                    long readStartTime = System.currentTimeMillis();

                    // 检查设备是否仍然可用
                    if (!isDeviceAvailable(deviceName)) {
                        log.info("检测到Android设备已关闭，自动停止视频流: {}", deviceName);
                        streamActive.set(false);
                        break;
                    }

                    // 高分辨率屏幕特别的内存管理
                    if (stats.isHighResolution() && readStartTime - lastMemoryCheckTime >= getMemoryCheckInterval(stats.getResolutionType())) {
                        checkMemoryUsageForHighRes(deviceName, stats.getResolutionType());
                        lastMemoryCheckTime = readStartTime;
                    }

                    // 背压处理 - 使用适合分辨率的阈值
                    if (handleBackpressure(outputStream, bytesRead, stats.getBackpressureThreshold())) {
                        // 2K屏幕需要更细致的背压处理
                        int delay = stats.isHighResolution() ? 2 : 1;
                        Thread.sleep(delay);
                    }

                    // 写入数据
                    outputStream.write(buffer, 0, bytesRead);
                    outputStream.flush();

                    // 更新统计信息
                    stats.updateStats(bytesRead);

                    long readEndTime = System.currentTimeMillis();
                    long readDuration = readEndTime - readStartTime;

                    // 动态调整缓冲区大小 - 2K屏幕更频繁调整
                    if (readEndTime - lastBufferAdjustTime >= BUFFER_ADJUSTMENT_INTERVAL) {
                        adjustBufferSizeForResolution(stats, buffer, readDuration, consecutiveSlowReads);
                        lastBufferAdjustTime = readEndTime;
                        buffer = new byte[stats.getCurrentBufferSize()]; // 重新分配缓冲区
                    }

                    // 跟踪慢读取 - 2K屏幕阈值更高
                    int slowReadThreshold = stats.isHighResolution() ? 200 : 100; // 2K屏幕200ms，普通100ms
                    if (readDuration > slowReadThreshold) {
                        consecutiveSlowReads++;
                    } else {
                        consecutiveSlowReads = 0;
                    }

                    // 定期记录性能日志
                    if (readEndTime - lastPerformanceLogTime >= PERFORMANCE_LOG_INTERVAL) {
                        logPerformanceStats(deviceName, stats);
                        lastPerformanceLogTime = readEndTime;
                    }
                }

                logFinalStats(deviceName, stats);

            } catch (IOException e) {
                log.error("Android设备视频流传输异常: 设备={}, 错误={}", deviceName, e.getMessage());
                streamActive.set(false);
                throw e;
            } catch (InterruptedException e) {
                log.warn("视频流传输被中断: {}", deviceName);
                streamActive.set(false);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("视频流传输发生未知错误: 设备={}, 错误={}", deviceName, e.getMessage(), e);
                streamActive.set(false);
                throw new IOException("视频流传输错误", e);
            } finally {
                // 清理资源
                cleanupStreamResources(deviceName, androidDevice, stats);
            }
        };
    }

    /**
     * 获取内存检查间隔
     */
    private long getMemoryCheckInterval(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return 3000; // 4K屏幕3秒检查一次
            case HIGH_2K: return 5000;       // 2K屏幕5秒检查一次
            default: return 10000;           // 普通屏幕10秒检查一次
        }
    }

    /**
     * 高分辨率屏幕内存使用检查
     */
    private void checkMemoryUsageForHighRes(String deviceName, ResolutionType resType) {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        
        // 不同分辨率使用不同的阈值
        double warningThreshold = getMemoryWarningThreshold(resType);
        double gcThreshold = getMemoryGcThreshold(resType);
        
        if (memoryUsagePercent > warningThreshold) {
            log.warn("{}屏幕传输内存使用率过高: {}% (设备: {}), 建议进行GC", 
                    resType.getDescription(), String.format("%.1f", memoryUsagePercent), deviceName);
            
            if (memoryUsagePercent > gcThreshold) {
                System.gc(); // 强制垃圾回收
                log.info("执行垃圾回收以释放内存 (设备: {}, 类型: {})", deviceName, resType.getDescription());
            }
        }
    }

    /**
     * 获取内存警告阈值
     */
    private double getMemoryWarningThreshold(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return 70.0; // 4K屏幕70%开始警告
            case HIGH_2K: return 80.0;       // 2K屏幕80%开始警告
            default: return 85.0;            // 普通屏幕85%开始警告
        }
    }

    /**
     * 获取内存GC阈值
     */
    private double getMemoryGcThreshold(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return 85.0; // 4K屏幕85%强制GC
            case HIGH_2K: return 90.0;       // 2K屏幕90%强制GC
            default: return 95.0;            // 普通屏幕95%强制GC
        }
    }

    /**
     * 背压处理 - 支持不同分辨率的阈值
     */
    private boolean handleBackpressure(java.io.OutputStream outputStream, int bytesRead, int threshold) {
        return bytesRead > threshold;
    }

    /**
     * 根据分辨率动态调整缓冲区大小 - 支持4K屏幕
     */
    private void adjustBufferSizeForResolution(StreamStatistics stats, byte[] currentBuffer,
                                               long readDuration, long consecutiveSlowReads) {
        int currentSize = stats.getCurrentBufferSize();
        int newSize = currentSize;
        ResolutionType resType = stats.getResolutionType();

        switch (resType) {
            case ULTRA_HIGH_4K:
                // 4K屏幕调整策略 - 最激进
                if (consecutiveSlowReads > 1 && currentSize < stats.getMaxBufferSize()) {
                    // 非常快的缓冲区增长
                    newSize = Math.min((int)(currentSize * 2.0), stats.getMaxBufferSize());
                } else if (readDuration < 30 && stats.getAvgThroughput() > 10 * 1024 * 1024 &&
                        currentSize > stats.getMinBufferSize()) {
                    // 高吞吐量时适当减小缓冲区
                    newSize = Math.max((int)(currentSize * 0.9), stats.getMinBufferSize());
                }
                break;
                
            case HIGH_2K:
                // 2K屏幕调整策略
                if (consecutiveSlowReads > 2 && currentSize < stats.getMaxBufferSize()) {
                    // 更快的缓冲区增长
                    newSize = Math.min((int)(currentSize * 1.5), stats.getMaxBufferSize());
                } else if (readDuration < 50 && stats.getAvgThroughput() > 5 * 1024 * 1024 &&
                        currentSize > stats.getMinBufferSize()) {
                    // 高吞吐量时适当减小缓冲区
                    newSize = Math.max((int)(currentSize * 0.8), stats.getMinBufferSize());
                }
                break;
                
            case STANDARD:
                // 普通屏幕调整策略
                if (consecutiveSlowReads > 3 && currentSize < stats.getMaxBufferSize()) {
                    newSize = Math.min(currentSize * 2, stats.getMaxBufferSize());
                } else if (readDuration < 10 && stats.getAvgThroughput() > 1024 * 1024 &&
                        currentSize > stats.getMinBufferSize()) {
                    newSize = Math.max(currentSize / 2, stats.getMinBufferSize());
                }
                break;
        }

        if (newSize != currentSize) {
            stats.setCurrentBufferSize(newSize);
            log.debug("调整设备 {} 缓冲区大小: {} -> {} KB ({})",
                    getCurrentDeviceName(), currentSize / 1024, newSize / 1024, resType.getDescription());
        }
    }

    /**
     * 创建优化的HTTP响应头 - 2K/4K屏幕优化
     */
    private HttpHeaders createOptimizedHeaders(String deviceName, int width, int height, int bitRate) {
        HttpHeaders headers = new HttpHeaders();
        ResolutionType resType = determineResolutionType(width, height);

        // 基本媒体类型
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // 缓存控制 - 针对流媒体优化
        headers.set("Cache-Control", "no-cache, no-store, must-revalidate, max-age=0");
        headers.set("Pragma", "no-cache");
        headers.set("Expires", "0");

        // 连接管理 - 根据分辨率类型设置不同的超时
        String keepAlive = getKeepAliveHeader(resType);
        headers.set("Connection", "keep-alive");
        headers.set("Keep-Alive", keepAlive);

        // 流媒体相关头
        headers.set("Accept-Ranges", "none");
        headers.set("Transfer-Encoding", "chunked");

        // 设备和流信息
        headers.set("X-Device-Name", deviceName);
        headers.set("X-Video-Resolution", formatResolution(width, height));
        headers.set("X-Video-BitRate", formatBitRate(bitRate));
        headers.set("X-Stream-Type", "android-screencap");
        headers.set("X-Buffer-Strategy", getBufferStrategy(resType));
        headers.set("X-Resolution-Type", getResolutionTypeHeader(resType));

        // 高分辨率屏幕特别的优化提示
        if (resType != ResolutionType.STANDARD) {
            headers.set("X-Optimization-Mode", "high-resolution");
            headers.set("X-Buffer-Size-Range", getBufferSizeRange(resType));
            headers.set("X-Memory-Strategy", getMemoryStrategy(resType));
        }

        // CORS支持（如果需要）
        headers.set("Access-Control-Allow-Origin", "*");
        headers.set("Access-Control-Allow-Methods", "GET");
        headers.set("Access-Control-Allow-Headers", "Content-Type");

        return headers;
    }

    /**
     * 获取Keep-Alive头信息
     */
    private String getKeepAliveHeader(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return "timeout=900, max=1000"; // 4K屏幕15分钟
            case HIGH_2K: return "timeout=600, max=1000";       // 2K屏幕10分钟
            default: return "timeout=300, max=1000";            // 普通屏幕5分钟
        }
    }

    /**
     * 获取缓冲区策略标识
     */
    private String getBufferStrategy(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return "adaptive-4k";
            case HIGH_2K: return "adaptive-2k";
            default: return "adaptive";
        }
    }

    /**
     * 获取分辨率类型头信息
     */
    private String getResolutionTypeHeader(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return "ultra-high-4k";
            case HIGH_2K: return "high-res-2k";
            default: return "standard";
        }
    }

    /**
     * 获取缓冲区大小范围信息
     */
    private String getBufferSizeRange(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: 
                return String.format("%d-%d", MIN_BUFFER_SIZE_4K / 1024, MAX_BUFFER_SIZE_4K / 1024);
            case HIGH_2K: 
                return String.format("%d-%d", MIN_BUFFER_SIZE_2K / 1024, MAX_BUFFER_SIZE_2K / 1024);
            default: 
                return String.format("%d-%d", MIN_BUFFER_SIZE / 1024, MAX_BUFFER_SIZE / 1024);
        }
    }

    /**
     * 获取内存策略标识
     */
    private String getMemoryStrategy(ResolutionType resType) {
        switch (resType) {
            case ULTRA_HIGH_4K: return "aggressive-gc";  // 4K屏幕激进GC
            case HIGH_2K: return "moderate-gc";          // 2K屏幕适度GC
            default: return "standard-gc";               // 普通屏幕标准GC
        }
    }

    /**
     * 记录性能统计信息 - 增强2K/4K屏幕监控
     */
    private void logPerformanceStats(String deviceName, StreamStatistics stats) {
        double throughputMB = stats.getAvgThroughput() / (1024 * 1024);
        String resType = stats.getResolutionType().getDescription();
        
        log.info("设备 {} {}分辨率流传输性能: 分辨率={}x{}, 总字节={}MB, 总帧数={}, 平均吞吐量={:.2f}MB/s, 缓冲区={}KB",
                deviceName, resType,
                stats.getScreenWidth(), stats.getScreenHeight(),
                stats.getTotalBytes() / (1024 * 1024),
                stats.getTotalFrames(),
                throughputMB,
                stats.getCurrentBufferSize() / 1024);
                
        // 高分辨率屏幕额外的内存监控
        if (stats.isHighResolution()) {
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            double memoryUsagePercent = (double) usedMemory / runtime.maxMemory() * 100;
            log.info("设备 {} {}流内存使用: {:.1f}% ({}MB/{}MB)",
                    deviceName, resType, memoryUsagePercent,
                    usedMemory / (1024 * 1024),
                    runtime.maxMemory() / (1024 * 1024));
        }
    }

    /**
     * 记录最终统计信息 - 增强2K/4K屏幕统计
     */
    private void logFinalStats(String deviceName, StreamStatistics stats) {
        long duration = System.currentTimeMillis() - stats.startTime;
        double durationSec = duration / 1000.0;
        double avgFPS = durationSec > 0 ? stats.getTotalFrames() / durationSec : 0.0;
        double finalThroughputMB = stats.getAvgThroughput() / (1024 * 1024);
        String resType = stats.getResolutionType().getDescription();

        log.info("设备 {} {}视频流传输完成统计: 分辨率={}x{}, 总时长={:.2f}s, 总字节={}MB, 总帧数={}, 平均FPS={:.2f}, 平均吞吐量={:.2f}MB/s",
                deviceName, resType,
                stats.getScreenWidth(), stats.getScreenHeight(),
                durationSec,
                stats.getTotalBytes() / (1024 * 1024),
                stats.getTotalFrames(),
                avgFPS,
                finalThroughputMB);
    }

    /**
     * 获取当前设备名称（用于日志）
     */
    private String getCurrentDeviceName() {
        // 这里可以从当前线程上下文获取设备名称
        // 简化实现，返回占位符
        return "current-device";
    }

    /**
     * 清理流资源 - 增强2K屏幕资源管理
     */
    private void cleanupStreamResources(String deviceName, AndroidDevice androidDevice, StreamStatistics stats) {
        try {
            // 移除活跃流标记
            activeStreams.remove(deviceName);

            // 移除统计信息
            if (stats != null) {
                streamStats.remove(deviceName);
                
                // 2K屏幕特别的清理建议
                if (stats.isHighResolution()) {
                    log.info("清理2K高分辨率流资源: 设备={}, 总传输={}MB", 
                            deviceName, stats.getTotalBytes() / (1024 * 1024));
                    
                    // 对于2K屏幕，建议进行一次垃圾回收
                    if (stats.getTotalBytes() > 100 * 1024 * 1024) { // 传输超过100MB
                        System.gc();
                        log.debug("2K流清理后执行垃圾回收: {}", deviceName);
                    }
                }
            }

            // 停止视频流
            if (androidDevice != null) {
                androidDevice.stopVideoStream();
                log.info("已停止Android设备视频流: {}", deviceName);
            }
        } catch (Exception e) {
            log.warn("清理视频流资源时出错: 设备={}, 错误={}", deviceName, e.getMessage());
        }
    }

    /**
     * 检查设备是否仍然可用 - 2K屏幕优化检查
     */
    private boolean isDeviceAvailable(String deviceName) {
        try {
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice == null) {
                return false;
            }
            
            boolean isRegistered = deviceRegisterManager.isRegistered(deviceName);
            
            // 2K屏幕设备额外的健康检查
            StreamStatistics stats = streamStats.get(deviceName);
            if (stats != null && stats.isHighResolution()) {
                // 检查内存压力
                Runtime runtime = Runtime.getRuntime();
                long usedMemory = runtime.totalMemory() - runtime.freeMemory();
                double memoryUsagePercent = (double) usedMemory / runtime.maxMemory() * 100;
                
                if (memoryUsagePercent > 95) {
                    log.warn("2K设备 {} 因内存压力过高可能不可用: {:.1f}%", deviceName, memoryUsagePercent);
                    return false;
                }
            }
            
            return isRegistered;
        } catch (Exception e) {
            log.warn("检查设备可用性时出错: 设备={}, 错误={}", deviceName, e.getMessage());
            return false;
        }
    }

    /**
     * 获取Android设备实例
     */
    private AndroidDevice getAndroidDevice(String deviceName) {
        try {
            Object device = deviceRegisterManager.getDevice(deviceName);
            if (device instanceof AndroidDevice) {
                return (AndroidDevice) device;
            } else {
                log.warn("设备 {} 不是Android设备类型: {}", deviceName,
                        device != null ? device.getClass().getSimpleName() : "null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取Android设备失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 格式化分辨率字符串 - 增强2K/4K屏幕标识
     */
    private String formatResolution(int width, int height) {
        if (width <= 0 || height <= 0) {
            return "default";
        }
        
        String resolution = width + "x" + height;
        ResolutionType resType = determineResolutionType(width, height);
        
        // 添加分辨率类型标识
        switch (resType) {
            case ULTRA_HIGH_4K:
                if (width >= 7680 || height >= 4320) {
                    resolution += " (8K)";
                } else if (width >= 5120 || height >= 2880) {
                    resolution += " (5K)";
                } else {
                    resolution += " (4K)";
                }
                break;
            case HIGH_2K:
                if (width >= 2560 || height >= 1440) {
                    resolution += " (2K+)";
                } else {
                    resolution += " (2K)";
                }
                break;
            default:
                // 标准分辨率不添加后缀
                break;
        }
        
        return resolution;
    }

    /**
     * 格式化比特率字符串
     */
    private String formatBitRate(int bitRate) {
        if (bitRate <= 0) {
            return "default";
        }
        if (bitRate >= 1000000) {
            return (bitRate / 1000000) + "Mbps";
        } else if (bitRate >= 1000) {
            return (bitRate / 1000) + "Kbps";
        } else {
            return bitRate + "bps";
        }
    }

    /**
     * 获取当前活跃的视频流数量
     */
    public int getActiveStreamCount() {
        return activeStreams.size();
    }

    /**
     * 获取活跃的高分辨率流数量
     */
    public int getActiveHighResStreamCount() {
        return (int) streamStats.values().stream()
                .filter(stats -> stats != null && stats.isHighResolution())
                .count();
    }

    /**
     * 获取活跃的4K流数量
     */
    public int getActive4KStreamCount() {
        return (int) streamStats.values().stream()
                .filter(stats -> stats != null && stats.is4KResolution())
                .count();
    }

    /**
     * 获取活跃的2K流数量
     */
    public int getActive2KStreamCount() {
        return (int) streamStats.values().stream()
                .filter(stats -> stats != null && stats.is2KResolution())
                .count();
    }

    /**
     * 检查指定设备是否有活跃的视频流
     */
    public boolean hasActiveStream(String deviceName) {
        AtomicBoolean streamActive = activeStreams.get(deviceName);
        return streamActive != null && streamActive.get();
    }

    /**
     * 获取设备流统计信息
     */
    public StreamStatistics getStreamStatistics(String deviceName) {
        return streamStats.get(deviceName);
    }

    /**
     * 手动停止指定设备的视频流
     */
    public void forceStopStream(String deviceName) {
        AtomicBoolean streamActive = activeStreams.get(deviceName);
        if (streamActive != null) {
            streamActive.set(false);
            log.info("手动停止设备视频流: {}", deviceName);
        }
    }

    /**
     * 停止所有活跃的视频流 - 增强2K屏幕处理
     */
    public void stopAllStreams() {
        int total2K = getActive2KStreamCount();
        log.info("停止所有活跃的Android设备视频流，当前数量: {} (其中2K流: {})", activeStreams.size(), total2K);
        
        for (String deviceName : activeStreams.keySet()) {
            forceStopStream(deviceName);
        }
        activeStreams.clear();
        streamStats.clear();
        
        // 如果有2K流，建议进行垃圾回收
        if (total2K > 0) {
            System.gc();
            log.info("已停止{}个2K流，执行垃圾回收释放内存", total2K);
        }
    }

    /**
     * 获取所有流的性能概览 - 增强2K屏幕信息
     */
    @GetMapping("/stats/overview")
    public ResponseEntity<String> getPerformanceOverview() {
        StringBuilder overview = new StringBuilder();
        int total2K = getActive2KStreamCount();
        
        overview.append("当前活跃流数量: ").append(activeStreams.size())
                .append(" (其中2K流: ").append(total2K).append(")\n");

        // 系统内存信息
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        double memoryUsagePercent = (double) usedMemory / runtime.maxMemory() * 100;
        overview.append("系统内存使用: ").append(String.format("%.1f%%", memoryUsagePercent))
                .append(" (").append(usedMemory / (1024 * 1024)).append("MB/")
                .append(runtime.maxMemory() / (1024 * 1024)).append("MB)\n");

        for (String deviceName : streamStats.keySet()) {
            StreamStatistics stats = streamStats.get(deviceName);
            if (stats != null) {
                String resType = stats.isHighResolution() ? "2K" : "标准";
                overview.append("设备: ").append(deviceName)
                        .append(" (").append(resType).append(")")
                        .append(", 分辨率: ").append(stats.getScreenWidth()).append("x").append(stats.getScreenHeight())
                        .append(", 总字节: ").append(stats.getTotalBytes() / (1024 * 1024)).append("MB")
                        .append(", 吞吐量: ").append(String.format("%.2f", stats.getAvgThroughput() / (1024 * 1024))).append("MB/s")
                        .append(", 缓冲区: ").append(stats.getCurrentBufferSize() / 1024).append("KB")
                        .append("\n");
            }
        }

        return ResponseEntity.ok(overview.toString());
    }

    /**
     * 获取2K流专用统计信息
     */
    @GetMapping("/stats/2k-overview")
    public ResponseEntity<String> get2KStreamOverview() {
        StringBuilder overview = new StringBuilder();
        int total2K = getActive2KStreamCount();
        
        overview.append("2K高分辨率流统计概览\n");
        overview.append("===================\n");
        overview.append("当前2K活跃流数量: ").append(total2K).append("\n");

        if (total2K > 0) {
            long total2KBytes = 0;
            double avg2KThroughput = 0;
            int count = 0;

            for (String deviceName : streamStats.keySet()) {
                StreamStatistics stats = streamStats.get(deviceName);
                if (stats != null && stats.isHighResolution()) {
                    total2KBytes += stats.getTotalBytes();
                    avg2KThroughput += stats.getAvgThroughput();
                    count++;
                    
                    overview.append("2K设备: ").append(deviceName)
                            .append(", 分辨率: ").append(stats.getScreenWidth()).append("x").append(stats.getScreenHeight())
                            .append(", 传输: ").append(stats.getTotalBytes() / (1024 * 1024)).append("MB")
                            .append(", 缓冲区: ").append(stats.getCurrentBufferSize() / 1024).append("KB")
                            .append("\n");
                }
            }
            
            if (count > 0) {
                overview.append("\n2K流汇总:\n");
                overview.append("总传输量: ").append(total2KBytes / (1024 * 1024)).append("MB\n");
                overview.append("平均吞吐量: ").append(String.format("%.2f", (avg2KThroughput / count) / (1024 * 1024))).append("MB/s\n");
            }
        } else {
            overview.append("当前没有活跃的2K流\n");
        }

        return ResponseEntity.ok(overview.toString());
    }
}
